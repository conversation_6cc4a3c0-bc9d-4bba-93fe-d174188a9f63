Stack trace:
Frame         Function      Args
0007FFFFAF30  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF9E30) msys-2.0.dll+0x1FE8E
0007FFFFAF30  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFB208) msys-2.0.dll+0x67F9
0007FFFFAF30  000210046832 (000210286019, 0007FFFFADE8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFAF30  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFAF30  000210068E24 (0007FFFFAF40, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFB210  00021006A225 (0007FFFFAF40, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD5FD80000 ntdll.dll
7FFD3C8A0000 aswhook.dll
7FFD5F000000 KERNEL32.DLL
7FFD5D010000 KERNELBASE.dll
7FFD5F410000 USER32.dll
7FFD5DAF0000 win32u.dll
7FFD5F3E0000 GDI32.dll
7FFD5D400000 gdi32full.dll
7FFD5CF60000 msvcp_win.dll
7FFD5D760000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFD5F320000 advapi32.dll
7FFD5FAE0000 msvcrt.dll
7FFD5FC90000 sechost.dll
7FFD5DBA0000 RPCRT4.dll
7FFD5C4D0000 CRYPTBASE.DLL
7FFD5D540000 bcryptPrimitives.dll
7FFD5E010000 IMM32.DLL
