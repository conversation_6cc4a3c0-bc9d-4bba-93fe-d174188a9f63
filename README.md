# 🦴 Bone Quest - Orthopaedic AI Assistant

A full-stack webapp that provides intelligent answers to orthopaedic questions using a hybrid RAG (Retrieval-Augmented Generation) system.

## 🌟 Features

- **🔍 Semantic Search**: FAISS vector database with SentenceTransformers for intelligent document retrieval
- **🤖 Hybrid AI**: Local Ollama (Mistral) with OpenRouter (DeepSeek) fallback for robust responses
- **📄 PDF Integration**: Add research papers, textbooks, and clinical guidelines to knowledge base
- **🗣️ Text-to-Speech**: Coqui TTS integration for audio responses (optional)
- **💬 Modern UI**: Clean, responsive chat interface
- **⚡ Fast API**: FastAPI backend with real-time processing

## 🏗️ Architecture

```
[ Frontend (HTML/JS) ]
         |
         | HTTP/REST
         v
[ FastAPI Backend ]
         |
         |-> [FAISS Vector DB] (semantic retrieval)
         |-> [Ollama/Mistral] (local LLM)
         |-> [OpenRouter/DeepSeek] (fallback LLM)
         |-> [Coqui TTS] (speech synthesis)
```

## 🚀 Quick Start

### Prerequisites

- Python 3.8+
- Node.js (optional, for React frontend)
- Ollama (optional, for local LLM)

### Installation

1. **Clone and setup environment:**
```bash
git clone <repository>
cd BoneQuestProject
python -m venv venv
source venv/bin/activate  # On Windows: .\venv\Scripts\activate
pip install -r requirements.txt
```

2. **Build knowledge base:**
```bash
python build_index.py
```

3. **Configure environment:**
Create `.env` file with your OpenRouter API key:
```
OPENROUTER_API_KEY=your_api_key_here
```

4. **Start the server:**
```bash
python backend/main.py
```

5. **Open webapp:**
Navigate to `http://localhost:8000`

## 📁 Project Structure

```
BoneQuestProject/
├── backend/
│   └── main.py              # FastAPI server with RAG system
├── frontend/
│   └── index.html           # Chat interface
├── data/
│   ├── ortho_knowledge.txt  # Knowledge base documents
│   ├── kb.index            # FAISS vector index
│   ├── kb_docs.pkl         # Serialized documents
│   └── kb_model.pkl        # SentenceTransformer model
├── static/                  # Generated audio files
├── build_index.py          # Knowledge base builder
├── requirements.txt        # Python dependencies
└── .env                    # Environment configuration
```

## 🔧 Configuration

### Environment Variables

- `OPENROUTER_API_KEY`: Your OpenRouter API key for DeepSeek access
- `OLLAMA_URL`: Ollama API endpoint (default: http://localhost:11434/api/generate)
- `TTS_MODEL`: Coqui TTS model name (default: tts_models/en/vctk/vits)

### Knowledge Base

The system uses a curated orthopaedic knowledge base covering:
- Osteoarthritis and joint diseases
- ACL reconstruction and sports injuries
- Rotator cuff and shoulder problems
- Hip fractures and treatments
- Spinal stenosis
- Carpal tunnel syndrome
- Meniscus tears
- Plantar fasciitis
- Fracture healing
- Tennis elbow

To add more content:
- **Text content**: Edit `data/ortho_knowledge.txt` and run `python build_index.py`
- **PDF documents**: Use `python add_pdf_to_kb.py "path/to/pdfs/"` (see PDF_GUIDE.md)

## 🧪 Testing

### API Endpoints

- `GET /`: Frontend interface
- `GET /health`: System health check
- `POST /query`: Submit orthopaedic questions

### Example API Usage

```bash
curl -X POST "http://localhost:8000/query" \
  -H "Content-Type: application/json" \
  -d '{"question": "What is osteoarthritis?"}'
```

## 🎯 Usage Examples

Try asking questions like:
- "What are the symptoms of ACL tears?"
- "How is rotator cuff surgery performed?"
- "What causes hip fractures in elderly patients?"
- "Explain the treatment options for carpal tunnel syndrome"

## 🔄 AI Model Fallback

The system uses a smart fallback strategy:

1. **Primary**: Local Ollama with Mistral model
2. **Fallback**: OpenRouter with DeepSeek model
3. **Final**: Generic error message

This ensures high availability and response quality.

## 🎵 Audio Features

When TTS is available, the system generates audio responses automatically. Audio files are served from `/static/` and played in the browser.

## 🛠️ Development

### Adding New Knowledge

**From Text Files:**
1. Edit `data/ortho_knowledge.txt`
2. Run `python build_index.py`
3. Restart the server

**From PDF Documents:**
1. Place PDFs in `pdfs/` directory
2. Run `python add_pdf_to_kb.py "pdfs/"`
3. Restart the server

See `PDF_GUIDE.md` for detailed PDF integration instructions.

### Customizing the Frontend

The frontend is a single HTML file with embedded CSS and JavaScript. Modify `frontend/index.html` to customize the interface.

### Extending the API

Add new endpoints in `backend/main.py`. The FastAPI framework provides automatic documentation at `/docs`.

## 📊 Performance

- **Response Time**: ~2-5 seconds (depending on model)
- **Knowledge Base**: 10 documents, 384-dimensional embeddings
- **Concurrent Users**: Supports multiple simultaneous queries
- **Memory Usage**: ~500MB (with loaded models)

## 🔒 Security

- CORS enabled for development
- Input validation and sanitization
- Rate limiting recommended for production
- API key protection for external services

## 🚀 Deployment

For production deployment:

1. Use a production WSGI server (e.g., Gunicorn)
2. Set up reverse proxy (e.g., Nginx)
3. Configure environment variables securely
4. Enable HTTPS
5. Set up monitoring and logging

## 📝 License

This project is for educational and research purposes. Please ensure compliance with all AI model licenses and terms of service.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📞 Support

For issues and questions:
- Check the health endpoint: `/health`
- Review server logs for errors
- Ensure all dependencies are installed
- Verify API keys are configured correctly

---

**Built with ❤️ for the orthopaedic community**
