# 📄 Adding PDF Documents to Bone Quest Knowledge Base

This guide shows you how to add PDF documents (research papers, medical textbooks, clinical guidelines) to your Bone Quest knowledge base.

## 🚀 Quick Start

### Add a Single PDF
```bash
python add_pdf_to_kb.py "path/to/your/document.pdf"
```

### Add All PDFs from a Directory
```bash
python add_pdf_to_kb.py "path/to/pdf/directory/"
```

### Replace Entire Knowledge Base
```bash
python add_pdf_to_kb.py "path/to/pdfs/" --replace
```

## 📋 Prerequisites

The required libraries are automatically installed:
- **PyPDF2**: Basic PDF text extraction
- **PyMuPDF**: Advanced PDF processing (better quality)

## 🔧 Command Options

| Option | Description | Example |
|--------|-------------|---------|
| `path` | PDF file or directory path | `"documents/ortho.pdf"` |
| `--chunk-size` | Text chunk size (default: 1000) | `--chunk-size 1500` |
| `--replace` | Replace existing knowledge base | `--replace` |
| `--dry-run` | Preview without making changes | `--dry-run` |

## 📚 Examples

### 1. Add Research Papers
```bash
# Add a single research paper
python add_pdf_to_kb.py "research/acl_reconstruction_study.pdf"

# Add all papers from research directory
python add_pdf_to_kb.py "research/" --chunk-size 800
```

### 2. Add Medical Textbooks
```bash
# Add textbook chapters (larger chunks for context)
python add_pdf_to_kb.py "textbooks/" --chunk-size 1500
```

### 3. Preview Before Adding
```bash
# See what content would be extracted
python add_pdf_to_kb.py "new_document.pdf" --dry-run
```

### 4. Start Fresh with New PDFs
```bash
# Replace entire knowledge base with new PDFs
python add_pdf_to_kb.py "new_knowledge_base/" --replace
```

## 📁 Recommended Directory Structure

```
BoneQuestProject/
├── pdfs/
│   ├── research_papers/
│   │   ├── acl_reconstruction.pdf
│   │   ├── rotator_cuff_repair.pdf
│   │   └── osteoarthritis_treatment.pdf
│   ├── textbooks/
│   │   ├── orthopedic_surgery_chapter1.pdf
│   │   └── sports_medicine_guide.pdf
│   └── guidelines/
│       ├── aao_guidelines.pdf
│       └── clinical_protocols.pdf
└── data/
    ├── kb.index (auto-generated)
    ├── kb_docs.pkl (auto-generated)
    └── ortho_knowledge.txt (updated)
```

## 🎯 Best Practices

### 1. **Document Quality**
- Use high-quality PDFs with selectable text
- Avoid scanned images without OCR
- Ensure PDFs are not password-protected

### 2. **Chunk Size Guidelines**
- **Research Papers**: 800-1000 characters (detailed sections)
- **Textbooks**: 1200-1500 characters (broader context)
- **Guidelines**: 600-800 characters (specific protocols)

### 3. **Content Organization**
- Group similar documents in directories
- Use descriptive filenames
- Add documents incrementally to test quality

### 4. **Testing New Content**
- Always use `--dry-run` first to preview extraction
- Test with a small set of documents initially
- Verify content quality in the webapp

## 🔍 Troubleshooting

### Common Issues

**1. No text extracted from PDF**
```bash
# Problem: Scanned PDF or image-based content
# Solution: Use OCR tools or find text-based version
```

**2. Poor text quality**
```bash
# Problem: Formatting issues or garbled text
# Solution: Try different chunk sizes or clean the PDF
python add_pdf_to_kb.py "document.pdf" --chunk-size 500
```

**3. Memory issues with large PDFs**
```bash
# Problem: Very large documents
# Solution: Use smaller chunk sizes
python add_pdf_to_kb.py "large_book.pdf" --chunk-size 800
```

### Verification Steps

1. **Check extraction quality:**
```bash
python add_pdf_to_kb.py "test.pdf" --dry-run
```

2. **Verify knowledge base update:**
```bash
# Check if files were updated
ls -la data/
```

3. **Test in webapp:**
- Restart Bone Quest server
- Ask questions related to new content
- Verify relevant documents are retrieved

## 🔄 Workflow Example

Here's a complete workflow for adding new orthopaedic research:

```bash
# 1. Preview the content
python add_pdf_to_kb.py "new_research/knee_surgery.pdf" --dry-run

# 2. Add to knowledge base
python add_pdf_to_kb.py "new_research/knee_surgery.pdf"

# 3. Restart the server
python backend/main.py

# 4. Test in webapp
# Ask: "What are the latest techniques for knee surgery?"
```

## 📊 Monitoring Knowledge Base

### Check Current Status
```bash
# View current documents count
python -c "
import pickle
with open('data/kb_docs.pkl', 'rb') as f:
    docs = pickle.load(f)
print(f'Knowledge base contains {len(docs)} documents')
"
```

### Backup Knowledge Base
```bash
# Create backup before major changes
cp data/kb.index data/kb.index.backup
cp data/kb_docs.pkl data/kb_docs.pkl.backup
cp data/ortho_knowledge.txt data/ortho_knowledge.txt.backup
```

## 🎯 Content Recommendations

### High-Value PDF Sources
- **Research Papers**: PubMed, orthopedic journals
- **Clinical Guidelines**: AAOS, medical societies
- **Textbooks**: Orthopedic surgery references
- **Case Studies**: Clinical case reports
- **Protocols**: Hospital treatment protocols

### Content Types That Work Best
- ✅ Text-based PDFs with clear formatting
- ✅ Medical literature with structured content
- ✅ Clinical guidelines with bullet points
- ✅ Research abstracts and conclusions
- ❌ Image-heavy documents without text
- ❌ Heavily formatted tables (may need preprocessing)

## 🚀 After Adding PDFs

1. **Restart the server:**
```bash
python backend/main.py
```

2. **Test the new knowledge:**
- Ask questions related to the new content
- Check if relevant sources appear in responses
- Verify answer quality and accuracy

3. **Monitor performance:**
- Check response times with larger knowledge base
- Ensure semantic search still works effectively

---

**💡 Pro Tip**: Start with a few high-quality PDFs and gradually expand your knowledge base. This ensures optimal performance and content quality!

For more help, check the main README.md or ask questions in the Bone Quest webapp itself! 🦴
