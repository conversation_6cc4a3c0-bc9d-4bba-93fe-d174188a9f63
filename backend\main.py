#!/usr/bin/env python3
"""
FastAPI backend for Bone Quest - Orthopaedic AI Assistant
Implements RAG system with FAISS vector search, local Ollama LLM, and OpenRouter fallback.
"""

import os
import pickle
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path

import numpy as np
import faiss
import requests
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(title="Bone Quest API", description="Orthopaedic AI Assistant with RAG")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Serve frontend
from fastapi.responses import FileResponse

@app.get("/")
async def serve_frontend():
    """Serve the frontend HTML file."""
    return FileResponse("frontend/index.html")

# Configuration
OLLAMA_URL = "http://localhost:11434/api/generate"
OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY")
OPENROUTER_URL = "https://openrouter.ai/api/v1/chat/completions"
DEEPSEEK_MODEL = "deepseek/deepseek-r1-0528:free"

# Global variables for RAG system
faiss_index = None
documents = []
sentence_model = None

class QueryRequest(BaseModel):
    question: str
    max_length: int = 500

class QueryResponse(BaseModel):
    answer: str
    sources: List[str]
    audio_url: Optional[str] = None
    model_used: str

class RAGSystem:
    """Retrieval-Augmented Generation system for orthopaedic knowledge."""
    
    def __init__(self):
        self.index = None
        self.documents = []
        self.model = None
        self.load_knowledge_base()
    
    def load_knowledge_base(self):
        """Load FAISS index, documents, and model."""
        try:
            # Load FAISS index
            index_path = "data/kb.index"
            if os.path.exists(index_path):
                self.index = faiss.read_index(index_path)
                logger.info(f"Loaded FAISS index with {self.index.ntotal} documents")
            else:
                logger.warning(f"FAISS index not found at {index_path}")
                return False
            
            # Load documents
            docs_path = "data/kb_docs.pkl"
            if os.path.exists(docs_path):
                with open(docs_path, 'rb') as f:
                    self.documents = pickle.load(f)
                logger.info(f"Loaded {len(self.documents)} documents")
            else:
                logger.warning(f"Documents file not found at {docs_path}")
                return False
            
            # Load model
            model_path = "data/kb_model.pkl"
            if os.path.exists(model_path):
                with open(model_path, 'rb') as f:
                    self.model = pickle.load(f)
                logger.info("Loaded SentenceTransformer model")
            else:
                logger.warning(f"Model file not found at {model_path}")
                # Try to load model directly
                try:
                    from sentence_transformers import SentenceTransformer
                    self.model = SentenceTransformer('sentence-transformers/all-MiniLM-L6-v2')
                    logger.info("Loaded SentenceTransformer model directly")
                except Exception as e:
                    logger.error(f"Failed to load SentenceTransformer: {e}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error loading knowledge base: {e}")
            return False
    
    def retrieve_relevant_docs(self, query: str, k: int = 3) -> List[str]:
        """Retrieve top-k relevant documents for the query."""
        if not self.index or not self.model:
            logger.warning("Knowledge base not loaded")
            return []
        
        try:
            # Encode query
            query_embedding = self.model.encode([query])
            query_embedding = np.array(query_embedding).astype('float32')
            
            # Search
            distances, indices = self.index.search(query_embedding, k)
            
            # Get relevant documents
            relevant_docs = []
            for idx in indices[0]:
                if idx < len(self.documents):
                    relevant_docs.append(self.documents[idx])
            
            logger.info(f"Retrieved {len(relevant_docs)} relevant documents")
            return relevant_docs
            
        except Exception as e:
            logger.error(f"Error retrieving documents: {e}")
            return []

# Initialize RAG system
rag_system = RAGSystem()

def call_ollama(prompt: str, max_tokens: int = 500) -> Optional[str]:
    """Call local Ollama API with Mistral model."""
    try:
        payload = {
            "model": "mistral",
            "prompt": prompt,
            "stream": False,
            "options": {
                "num_predict": max_tokens,
                "temperature": 0.7
            }
        }
        
        response = requests.post(OLLAMA_URL, json=payload, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            answer = result.get("response", "").strip()
            logger.info(f"Ollama response length: {len(answer)}")
            return answer
        else:
            logger.warning(f"Ollama API error: {response.status_code}")
            return None
            
    except Exception as e:
        logger.error(f"Error calling Ollama: {e}")
        return None

def call_openrouter(prompt: str, max_tokens: int = 500) -> Optional[str]:
    """Call OpenRouter API with DeepSeek model as fallback."""
    if not OPENROUTER_API_KEY:
        logger.warning("OpenRouter API key not found")
        return None
    
    try:
        headers = {
            "Authorization": f"Bearer {OPENROUTER_API_KEY}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": DEEPSEEK_MODEL,
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "max_tokens": max_tokens,
            "temperature": 0.7
        }
        
        response = requests.post(OPENROUTER_URL, json=payload, headers=headers, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            answer = result["choices"][0]["message"]["content"].strip()
            logger.info(f"OpenRouter response length: {len(answer)}")
            return answer
        else:
            logger.warning(f"OpenRouter API error: {response.status_code}")
            return None
            
    except Exception as e:
        logger.error(f"Error calling OpenRouter: {e}")
        return None

def is_answer_sufficient(answer: str, min_length: int = 50) -> bool:
    """Check if the answer is sufficient or too generic."""
    if not answer or len(answer) < min_length:
        return False

    # Check for generic responses
    generic_phrases = [
        "i don't know",
        "i'm not sure",
        "i cannot",
        "i can't help",
        "consult a doctor",
        "see a healthcare provider"
    ]

    answer_lower = answer.lower()
    generic_count = sum(1 for phrase in generic_phrases if phrase in answer_lower)

    # If more than 30% of the answer is generic phrases, consider it insufficient
    return generic_count / len(answer.split()) < 0.3

def generate_tts(text: str, filename: str):
    """Generate text-to-speech audio file."""
    try:
        # Ensure static directory exists
        os.makedirs("static", exist_ok=True)

        # Try to import and use TTS
        try:
            from TTS.api import TTS

            # Initialize TTS model
            tts = TTS(model_name="tts_models/en/vctk/vits").to("cpu")

            # Generate speech
            output_path = f"static/{filename}"
            tts.tts_to_file(text=text, file_path=output_path, speaker="p225")

            logger.info(f"Generated TTS audio: {output_path}")

        except ImportError:
            logger.warning("TTS library not available, skipping audio generation")
        except Exception as e:
            logger.error(f"Error generating TTS: {e}")

    except Exception as e:
        logger.error(f"Error in TTS generation: {e}")

def generate_tts(text: str, filename: str):
    """Generate text-to-speech audio file."""
    try:
        # Ensure static directory exists
        os.makedirs("static", exist_ok=True)

        # Try to import and use TTS
        try:
            from TTS.api import TTS

            # Initialize TTS model
            tts = TTS(model_name="tts_models/en/vctk/vits").to("cpu")

            # Generate speech
            output_path = f"static/{filename}"
            tts.tts_to_file(text=text, file_path=output_path, speaker="p225")

            logger.info(f"Generated TTS audio: {output_path}")

        except ImportError:
            logger.warning("TTS library not available, skipping audio generation")
        except Exception as e:
            logger.error(f"Error generating TTS: {e}")

    except Exception as e:
        logger.error(f"Error in TTS generation: {e}")



@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "rag_loaded": rag_system.index is not None,
        "documents_count": len(rag_system.documents)
    }

@app.post("/query", response_model=QueryResponse)
async def query_ortho_ai(request: QueryRequest, background_tasks: BackgroundTasks):
    """Main endpoint for orthopaedic AI queries."""
    
    try:
        # Step 1: Retrieve relevant documents
        relevant_docs = rag_system.retrieve_relevant_docs(request.question, k=3)
        
        # Step 2: Build context-aware prompt
        context = "\n\n".join(relevant_docs) if relevant_docs else ""
        
        prompt = f"""You are an expert orthopaedic AI assistant. Answer the following question based on the provided context and your medical knowledge.

Context from knowledge base:
{context}

Question: {request.question}

Please provide a comprehensive, accurate answer focusing on orthopaedic medicine. If the context doesn't contain relevant information, use your general medical knowledge but clearly indicate this.

Answer:"""

        # Step 3: Try Ollama first
        answer = call_ollama(prompt, request.max_length)
        model_used = "ollama-mistral"
        
        # Step 4: Fallback to OpenRouter if needed
        if not answer or not is_answer_sufficient(answer):
            logger.info("Ollama response insufficient, trying OpenRouter...")
            answer = call_openrouter(prompt, request.max_length)
            model_used = "openrouter-deepseek"
        
        # Step 5: Final fallback
        if not answer:
            answer = "I apologize, but I'm unable to provide a response at the moment. Please try again later or consult with a healthcare professional."
            model_used = "fallback"
        
        # Step 6: Prepare response
        response = QueryResponse(
            answer=answer,
            sources=relevant_docs[:2],  # Return top 2 sources
            model_used=model_used
        )
        
        # Step 7: Generate TTS in background
        audio_filename = f"answer_{hash(answer) % 10000}.wav"
        background_tasks.add_task(generate_tts, answer, audio_filename)
        response.audio_url = f"/static/{audio_filename}"
        
        return response
        
    except Exception as e:
        logger.error(f"Error processing query: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
