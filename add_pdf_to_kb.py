#!/usr/bin/env python3
"""
Add PDF documents to the Bone Quest knowledge base.
This script extracts text from PDFs and rebuilds the FAISS index.
"""

import os
import sys
import pickle
import argparse
from pathlib import Path
from typing import List, Dict

try:
    import PyPDF2
except ImportError:
    print("PyPDF2 not installed. Installing...")
    os.system("pip install PyPDF2")
    import PyPDF2

try:
    import fitz  # PyMuPDF
    PYMUPDF_AVAILABLE = True
except ImportError:
    PYMUPDF_AVAILABLE = False
    print("PyMuPDF not available. Using PyPDF2 only.")

import numpy as np
import faiss
from sentence_transformers import SentenceTransformer

def extract_text_pypdf2(pdf_path: str) -> str:
    """Extract text from PDF using PyPDF2."""
    text = ""
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                text += page.extract_text() + "\n"
    except Exception as e:
        print(f"Error extracting text with PyPDF2 from {pdf_path}: {e}")
    return text

def extract_text_pymupdf(pdf_path: str) -> str:
    """Extract text from PDF using PyMuPDF (better quality)."""
    text = ""
    try:
        doc = fitz.open(pdf_path)
        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            text += page.get_text() + "\n"
        doc.close()
    except Exception as e:
        print(f"Error extracting text with PyMuPDF from {pdf_path}: {e}")
    return text

def extract_text_from_pdf(pdf_path: str) -> str:
    """Extract text from PDF using the best available method."""
    print(f"Extracting text from: {pdf_path}")
    
    # Try PyMuPDF first (better quality)
    if PYMUPDF_AVAILABLE:
        text = extract_text_pymupdf(pdf_path)
        if text.strip():
            print(f"✅ Extracted {len(text)} characters using PyMuPDF")
            return text
    
    # Fallback to PyPDF2
    text = extract_text_pypdf2(pdf_path)
    if text.strip():
        print(f"✅ Extracted {len(text)} characters using PyPDF2")
        return text
    
    print(f"❌ Failed to extract text from {pdf_path}")
    return ""

def clean_and_chunk_text(text: str, chunk_size: int = 1000, overlap: int = 200) -> List[str]:
    """Clean text and split into chunks for better retrieval."""
    # Basic text cleaning
    text = text.replace('\n\n', '\n')
    text = text.replace('\t', ' ')
    text = ' '.join(text.split())  # Remove extra whitespace
    
    if len(text) <= chunk_size:
        return [text] if text.strip() else []
    
    # Split into chunks with overlap
    chunks = []
    start = 0
    
    while start < len(text):
        end = start + chunk_size
        
        # Try to break at sentence boundary
        if end < len(text):
            # Look for sentence endings near the chunk boundary
            for i in range(end, max(start + chunk_size - 200, start), -1):
                if text[i] in '.!?':
                    end = i + 1
                    break
        
        chunk = text[start:end].strip()
        if chunk:
            chunks.append(chunk)
        
        start = end - overlap
        if start >= len(text):
            break
    
    return chunks

def load_existing_documents() -> List[str]:
    """Load existing documents from the knowledge base."""
    docs_path = "data/kb_docs.pkl"
    if os.path.exists(docs_path):
        with open(docs_path, 'rb') as f:
            return pickle.load(f)
    
    # Fallback to text file
    text_path = "data/ortho_knowledge.txt"
    if os.path.exists(text_path):
        with open(text_path, 'r', encoding='utf-8') as f:
            content = f.read()
        return [doc.strip() for doc in content.split('\n\n') if doc.strip()]
    
    return []

def save_updated_knowledge_base(documents: List[str]):
    """Save updated documents to text file."""
    os.makedirs("data", exist_ok=True)
    
    # Save to text file for backup
    with open("data/ortho_knowledge.txt", 'w', encoding='utf-8') as f:
        f.write('\n\n'.join(documents))
    
    print(f"✅ Saved {len(documents)} documents to ortho_knowledge.txt")

def rebuild_faiss_index(documents: List[str]):
    """Rebuild FAISS index with updated documents."""
    print("🔄 Rebuilding FAISS index...")
    
    # Load or create SentenceTransformer model
    model = SentenceTransformer('sentence-transformers/all-MiniLM-L6-v2')
    
    # Encode documents
    print("📊 Encoding documents...")
    embeddings = model.encode(documents, show_progress_bar=True)
    embeddings = np.array(embeddings).astype('float32')
    
    # Create FAISS index
    index = faiss.IndexFlatL2(embeddings.shape[1])
    index.add(embeddings)
    
    # Save everything
    os.makedirs("data", exist_ok=True)
    
    faiss.write_index(index, "data/kb.index")
    with open("data/kb_docs.pkl", 'wb') as f:
        pickle.dump(documents, f)
    with open("data/kb_model.pkl", 'wb') as f:
        pickle.dump(model, f)
    
    print(f"✅ FAISS index rebuilt with {len(documents)} documents")

def process_pdf_file(pdf_path: str, chunk_size: int = 1000) -> List[str]:
    """Process a single PDF file and return text chunks."""
    if not os.path.exists(pdf_path):
        print(f"❌ PDF file not found: {pdf_path}")
        return []
    
    # Extract text
    text = extract_text_from_pdf(pdf_path)
    if not text.strip():
        print(f"❌ No text extracted from {pdf_path}")
        return []
    
    # Clean and chunk
    chunks = clean_and_chunk_text(text, chunk_size)
    print(f"📄 Created {len(chunks)} chunks from {pdf_path}")
    
    return chunks

def process_pdf_directory(pdf_dir: str, chunk_size: int = 1000) -> List[str]:
    """Process all PDF files in a directory."""
    pdf_dir = Path(pdf_dir)
    if not pdf_dir.exists():
        print(f"❌ Directory not found: {pdf_dir}")
        return []
    
    all_chunks = []
    pdf_files = list(pdf_dir.glob("*.pdf"))
    
    if not pdf_files:
        print(f"❌ No PDF files found in {pdf_dir}")
        return []
    
    print(f"📚 Found {len(pdf_files)} PDF files")
    
    for pdf_file in pdf_files:
        chunks = process_pdf_file(str(pdf_file), chunk_size)
        all_chunks.extend(chunks)
    
    return all_chunks

def main():
    parser = argparse.ArgumentParser(description="Add PDF documents to Bone Quest knowledge base")
    parser.add_argument("path", help="Path to PDF file or directory containing PDFs")
    parser.add_argument("--chunk-size", type=int, default=1000, help="Size of text chunks (default: 1000)")
    parser.add_argument("--replace", action="store_true", help="Replace existing knowledge base instead of adding")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be added without making changes")
    
    args = parser.parse_args()
    
    print("🦴 Bone Quest - PDF Knowledge Base Updater")
    print("=" * 50)
    
    # Process PDFs
    if os.path.isfile(args.path):
        new_chunks = process_pdf_file(args.path, args.chunk_size)
    elif os.path.isdir(args.path):
        new_chunks = process_pdf_directory(args.path, args.chunk_size)
    else:
        print(f"❌ Invalid path: {args.path}")
        sys.exit(1)
    
    if not new_chunks:
        print("❌ No content extracted from PDFs")
        sys.exit(1)
    
    print(f"\n📊 Extracted {len(new_chunks)} chunks from PDFs")
    
    if args.dry_run:
        print("\n🔍 DRY RUN - Preview of extracted content:")
        for i, chunk in enumerate(new_chunks[:3]):
            print(f"\nChunk {i+1} (first 200 chars):")
            print(chunk[:200] + "..." if len(chunk) > 200 else chunk)
        if len(new_chunks) > 3:
            print(f"\n... and {len(new_chunks) - 3} more chunks")
        return
    
    # Load existing documents
    if args.replace:
        print("🔄 Replacing existing knowledge base...")
        all_documents = new_chunks
    else:
        print("📚 Loading existing knowledge base...")
        existing_docs = load_existing_documents()
        print(f"📖 Found {len(existing_docs)} existing documents")
        all_documents = existing_docs + new_chunks
    
    print(f"📊 Total documents: {len(all_documents)}")
    
    # Save and rebuild
    save_updated_knowledge_base(all_documents)
    rebuild_faiss_index(all_documents)
    
    print("\n✅ Knowledge base updated successfully!")
    print(f"📈 Total documents: {len(all_documents)}")
    print("🚀 Restart the Bone Quest server to use the updated knowledge base")

if __name__ == "__main__":
    main()
