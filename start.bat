@echo off
echo 🦴 Starting Bone Quest - Orthopaedic AI Assistant
echo.

REM Check if virtual environment exists
if not exist "venv\Scripts\activate.bat" (
    echo ❌ Virtual environment not found. Please run setup first.
    echo Run: python -m venv venv
    echo Then: .\venv\Scripts\activate
    echo Then: pip install -r requirements.txt
    pause
    exit /b 1
)

REM Check if knowledge base exists
if not exist "data\kb.index" (
    echo 📚 Knowledge base not found. Building it now...
    call venv\Scripts\activate.bat && python build_index.py
    if errorlevel 1 (
        echo ❌ Failed to build knowledge base
        pause
        exit /b 1
    )
)

REM Check if .env file exists
if not exist ".env" (
    echo ⚠️  Warning: .env file not found. OpenRouter fallback may not work.
    echo Create .env file with: OPENROUTER_API_KEY=your_key_here
    echo.
)

echo ✅ Starting Bone Quest server...
echo 🌐 Open http://localhost:8000 in your browser
echo 🛑 Press Ctrl+C to stop the server
echo.

call venv\Scripts\activate.bat && python backend\main.py
