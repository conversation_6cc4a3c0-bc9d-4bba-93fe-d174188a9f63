# 🚀 Quick Start: Adding PDFs to <PERSON> Quest

## 📄 What You Can Add

- **Research Papers** (PubMed articles, journal papers)
- **Medical Textbooks** (orthopedic surgery chapters)
- **Clinical Guidelines** (AAOS, medical society protocols)
- **Case Studies** (clinical case reports)
- **Treatment Protocols** (hospital procedures)

## ⚡ Quick Commands

### 1. Add a Single PDF
```bash
python add_pdf_to_kb.py "my_document.pdf"
```

### 2. Add All PDFs from a Folder
```bash
python add_pdf_to_kb.py "pdfs/"
```

### 3. Preview Before Adding (Recommended)
```bash
python add_pdf_to_kb.py "document.pdf" --dry-run
```

### 4. Replace Entire Knowledge Base
```bash
python add_pdf_to_kb.py "new_pdfs/" --replace
```

## 📋 Step-by-Step Example

1. **Create PDF directory:**
```bash
mkdir pdfs
```

2. **Add your PDF files to the directory**

3. **Preview what will be extracted:**
```bash
python add_pdf_to_kb.py "pdfs/" --dry-run
```

4. **Add to knowledge base:**
```bash
python add_pdf_to_kb.py "pdfs/"
```

5. **Restart Bone Quest:**
```bash
python backend/main.py
```

6. **Test in webapp:**
- Open http://localhost:8000
- Ask questions about your new content

## ⚙️ Advanced Options

| Command | Purpose | Example |
|---------|---------|---------|
| `--chunk-size 800` | Smaller chunks for detailed content | Research papers |
| `--chunk-size 1500` | Larger chunks for context | Textbooks |
| `--dry-run` | Preview without changes | Testing |
| `--replace` | Start fresh | New knowledge base |

## 🔍 Troubleshooting

**Problem: No text extracted**
- Solution: Ensure PDF has selectable text (not scanned image)

**Problem: Poor quality text**
- Solution: Try different chunk size: `--chunk-size 500`

**Problem: Server not finding new content**
- Solution: Restart the server after adding PDFs

## 📊 Verification

Check if PDFs were added successfully:
```bash
python -c "
import pickle
with open('data/kb_docs.pkl', 'rb') as f:
    docs = pickle.load(f)
print(f'Knowledge base now has {len(docs)} documents')
"
```

## 💡 Pro Tips

1. **Start small** - Add 1-2 PDFs first to test quality
2. **Use descriptive filenames** - Helps with organization
3. **Group similar content** - Put research papers in one folder
4. **Test extraction quality** - Always use `--dry-run` first
5. **Backup before major changes** - Copy `data/` folder

## 🎯 Best PDF Types

✅ **Works Great:**
- Text-based PDFs
- Research papers with clear structure
- Clinical guidelines with bullet points
- Medical textbooks with paragraphs

❌ **Avoid:**
- Scanned images without OCR
- Password-protected PDFs
- Heavily formatted tables
- Image-only documents

---

**Need more help?** Check `PDF_GUIDE.md` for detailed instructions!
