<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bone Quest - Orthopaedic AI Assistant</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 800px;
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }

        .chat-container {
            height: 500px;
            overflow-y: auto;
            padding: 20px;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 15px;
            max-width: 80%;
            word-wrap: break-word;
        }

        .user-message {
            background: #007bff;
            color: white;
            margin-left: auto;
            text-align: right;
        }

        .ai-message {
            background: white;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .ai-message .model-info {
            font-size: 0.8em;
            color: #6c757d;
            margin-bottom: 10px;
            font-style: italic;
        }

        .sources {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #e9ecef;
        }

        .sources h4 {
            color: #495057;
            margin-bottom: 10px;
            font-size: 0.9em;
        }

        .source {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 8px;
            font-size: 0.85em;
            color: #6c757d;
        }

        .input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
        }

        .input-group {
            display: flex;
            gap: 10px;
        }

        .question-input {
            flex: 1;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }

        .question-input:focus {
            border-color: #007bff;
        }

        .send-button {
            padding: 15px 30px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }

        .send-button:hover {
            background: #0056b3;
        }

        .send-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #6c757d;
        }

        .loading.show {
            display: block;
        }

        .audio-player {
            margin-top: 10px;
        }

        .audio-player audio {
            width: 100%;
            height: 40px;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .chat-container {
                height: 400px;
            }
            
            .message {
                max-width: 95%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🦴 Bone Quest</h1>
            <p>Your AI-Powered Orthopaedic Assistant</p>
        </div>
        
        <div class="chat-container" id="chatContainer">
            <div class="message ai-message">
                <div class="model-info">Welcome to Bone Quest!</div>
                <div>Hello! I'm your orthopaedic AI assistant. I can help answer questions about bone health, joint problems, injuries, and treatments. What would you like to know?</div>
            </div>
        </div>
        
        <div class="loading" id="loading">
            <div>🤖 Thinking... Please wait while I analyze your question.</div>
        </div>
        
        <div class="input-container">
            <div class="input-group">
                <input 
                    type="text" 
                    id="questionInput" 
                    class="question-input" 
                    placeholder="Ask me about orthopaedic conditions, treatments, or injuries..."
                    maxlength="500"
                >
                <button id="sendButton" class="send-button">Send</button>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8000';
        
        const chatContainer = document.getElementById('chatContainer');
        const questionInput = document.getElementById('questionInput');
        const sendButton = document.getElementById('sendButton');
        const loading = document.getElementById('loading');

        // Add message to chat
        function addMessage(content, isUser = false, modelInfo = '', sources = [], audioUrl = '') {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user-message' : 'ai-message'}`;
            
            let messageHTML = '';
            
            if (!isUser && modelInfo) {
                messageHTML += `<div class="model-info">Answered by: ${modelInfo}</div>`;
            }
            
            messageHTML += `<div>${content}</div>`;
            
            if (audioUrl) {
                messageHTML += `
                    <div class="audio-player">
                        <audio controls autoplay>
                            <source src="${audioUrl}" type="audio/wav">
                            Your browser does not support the audio element.
                        </audio>
                    </div>
                `;
            }
            
            if (sources && sources.length > 0) {
                messageHTML += '<div class="sources"><h4>📚 Sources:</h4>';
                sources.forEach((source, index) => {
                    messageHTML += `<div class="source">${index + 1}. ${source.substring(0, 150)}...</div>`;
                });
                messageHTML += '</div>';
            }
            
            messageDiv.innerHTML = messageHTML;
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        // Add error message
        function addErrorMessage(error) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.textContent = `Error: ${error}`;
            chatContainer.appendChild(errorDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        // Send question to API
        async function sendQuestion() {
            const question = questionInput.value.trim();
            if (!question) return;

            // Add user message
            addMessage(question, true);
            questionInput.value = '';
            
            // Show loading
            loading.classList.add('show');
            sendButton.disabled = true;

            try {
                const response = await fetch(`${API_BASE_URL}/query`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ question: question })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                
                // Add AI response
                addMessage(
                    data.answer, 
                    false, 
                    data.model_used, 
                    data.sources || [], 
                    data.audio_url || ''
                );

            } catch (error) {
                console.error('Error:', error);
                addErrorMessage(error.message || 'Failed to get response from AI');
            } finally {
                loading.classList.remove('show');
                sendButton.disabled = false;
                questionInput.focus();
            }
        }

        // Event listeners
        sendButton.addEventListener('click', sendQuestion);
        
        questionInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendQuestion();
            }
        });

        // Focus input on load
        questionInput.focus();
    </script>
</body>
</html>
