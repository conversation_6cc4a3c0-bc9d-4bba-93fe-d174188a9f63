@echo off
echo 🦴 Bone Quest Setup Script
echo.

echo 📦 Creating virtual environment...
python -m venv venv
if errorlevel 1 (
    echo ❌ Failed to create virtual environment
    echo Make sure Python 3.8+ is installed
    pause
    exit /b 1
)

echo 📥 Installing dependencies...
call venv\Scripts\activate.bat && pip install --upgrade pip
call venv\Scripts\activate.bat && pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

echo 📚 Building knowledge base...
call venv\Scripts\activate.bat && python build_index.py
if errorlevel 1 (
    echo ❌ Failed to build knowledge base
    pause
    exit /b 1
)

echo 🔧 Creating environment file...
if not exist ".env" (
    echo # OpenRouter API Configuration > .env
    echo OPENROUTER_API_KEY=your_api_key_here >> .env
    echo. >> .env
    echo # Ollama Configuration >> .env
    echo OLLAMA_URL=http://localhost:11434/api/generate >> .env
    echo. >> .env
    echo # Application Configuration >> .env
    echo DEBUG=True >> .env
    echo LOG_LEVEL=INFO >> .env
    
    echo ⚠️  Please edit .env file and add your OpenRouter API key
)

echo.
echo ✅ Setup complete!
echo.
echo 🚀 To start the application, run: start.bat
echo 🌐 Or manually: python backend\main.py
echo 📖 Then open: http://localhost:8000
echo.
pause
